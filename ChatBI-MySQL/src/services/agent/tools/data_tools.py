"""
Data-related tools for agent bots.
"""

import uuid
from typing import Tuple, List, Dict, Any
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.services.agent.tools.feishu_tools import upload_sql_result_to_feishu_if_needed
from src.services.xianmudb.query_service import execute_business_query, execute_business_query_async
from src.services.xianmudb.metadata_service import (
    get_table_sample_data as get_table_sample_data_real,
)
from src.models.query_result import SQLQueryResult
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager
from src.services.concurrency.user_query_limiter import user_query_limiter

async def fetch_mysql_sql_result(
    wrapper: RunContextWrapper[UserInfo], sql: str, description: str, upload_to_feishu: bool = False
) -> Tuple[SQLQueryResult, str]:
    """使用SQL查询MySQL数据库，返回查询结果和描述。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        sql: 要执行的SQL查询语句。
        description: 对SQL查询的简短描述。
        upload_to_feishu: 是否需要将查询结果上传到飞书（用户要求下载到Excel等操作也可以设置为True），默认为False。如果用户明确要求将结果上传到飞书，则设置为True。否则，设置为False。

    Returns:
        Tuple[SQLQueryResult, str]: 查询结果和对查询的描述。
    """
    # 获取用户信息用于并发控制
    user_info = wrapper.context
    user_id = user_info.email or user_info.user_name or "unknown_user"
    query_id = str(uuid.uuid4())

    logger.info(f"用户 {user_id} 开始执行SQL查询: {sql[:100]}...")

    # 使用用户级别的查询限制器，防止单个用户的慢查询影响其他用户
    result = await user_query_limiter.execute_with_limit(
        user_id=user_id,
        query_id=query_id,
        query_func=execute_business_query_async,
        sql_query=sql
    )

    if result is None:
        error_msg = f"查询被限制或超时，用户: {user_id}"
        logger.warning(error_msg)
        return None, f"{description} 执行失败: {error_msg}"

    if result.success:
        # Pass user_info from the wrapper context
        result = await upload_sql_result_to_feishu_if_needed(
            sql_result=result, sql_description=description, user_info=wrapper.context, upload_to_feishu=upload_to_feishu
        )
        return result, description
    else:
        # Return None for the result part of the tuple if the query failed
        return None, f"{description} 执行失败: {result.error}"


def get_sales_manager_team_members(bd_id = None, bd_name = None) -> SQLQueryResult:
    """
    根据销售主管的ID或者名字，获取销售主管（M1或者M2）的团队成员，包括直接下属和间接下属的销售代表，以及他们的所属运营大区。

    参数:
        bd_id: 销售主管的ID。【注意，千万不可编造ID】
        bd_name: 销售主管的名字。【注意，千万不可编造名字】
        如果 bd_id 和 bd_name 都提供，优先使用 bd_id。

    返回:
        SQLQueryResult: 销售主管的所有团队成员列表，包括销售代表的ID、名字和所属运营大区。
    """
    if bd_id is None and bd_name is None:
        return None
    
    # 尝试将bd_id转换为整数
    query_str = f"bd_name = '{bd_name}'"
    try:
        if bd_id is not None:
            bd_id_int = int(bd_id)
            query_str = f"bd_id = {bd_id_int}"
    except (ValueError, TypeError):
        # 如果bd_id无法转换为整数，则使用bd_name
        logger.error(f"bd_id无法转换为整数: {bd_id}, 使用bd_name: {bd_name}")
            
    sql = f"""SELECT
  t.bd_id,t.bd_name,GROUP_CONCAT(distinct large_area.large_area_name) 所属运营大区
FROM
  (
    SELECT
      `bd_id`,`bd_name`
    FROM
      `crm_bd_org`
    WHERE
      `parent_id` IN (
        SELECT id
        FROM crm_bd_org
        WHERE {query_str}
      )
    UNION ALL
    SELECT
      `bd_id`, `bd_name`
    FROM `crm_bd_org`
    WHERE
      `parent_id` IN (
        SELECT id FROM `crm_bd_org`
        WHERE
          `parent_id` IN (
            SELECT id
            FROM crm_bd_org
            WHERE {query_str}
          )
      )
  ) t
  JOIN `crm_bd_area` ON t.bd_id = crm_bd_area.admin_id
  JOIN `area` ON crm_bd_area.area_no = area.area_no
  JOIN `large_area` ON area.large_area_no = large_area.large_area_no
  JOIN `crm_bd_config` on `t`.bd_id = `crm_bd_config`.admin_id
WHERE crm_bd_config.gmv_target>10000
GROUP BY `t`.bd_id,t.bd_name;
    """
    result = execute_business_query(sql)
    if result.success:
        return result
    else:
        return None


def get_table_sample_data(table_name: str, sample_size: int = 2) -> SQLQueryResult:
    """
    获取指定表的样本数据。

    参数:
        table_name (str): 表名。
        sample_size (int): 样本大小，默认为2。

    使用示例:
        # 获取`orders`表的示例数据
        order_sample_data = get_table_sample_data('orders')

        # 获取最新的`merchants`表的示例数据
        order_sample_data = get_table_sample_data('merchants', 5)

    返回:
        dict: {"columns": columns, "data": list, "error": None}. list包含示例数据, columns为列名列表
    """
    return get_table_sample_data_real(table_name, sample_size)


def get_large_areas() -> List[Dict[str, Any]]:
    """
    获取公司当前定义的所有运营大区及其编号和名称。
    该工具主要用于：
    1. 当用户在提问中提及运营大区时，首先调用此工具获取所有有效的大区列表。
    2. 将用户提到的大区名称与列表中的实际大区名称进行匹配和验证。
    3. 如果用户提到的大区名称与列表中的实际大区名称不完全一致，AI应首先尝试进行智能匹配和纠正（例如，将 "杭粥大区" 自动识别为 "杭州大区"）。如果能够高置信度地匹配到一个有效大区，则可以直接使用纠正后的大区。只有当用户输入的大区名称偏差过大，完全无法匹配到任何已知大区时（例如 "美国大区"），才需要告知用户其输入的大区不存在，并可提示有效的大区选项。
    4. 对于用户可能提及的更广泛的地理区域（例如 "长三角地区"），AI可以尝试根据此工具返回的实际大区列表，智能地判断该广泛区域可能对应我司的哪一个或哪几个具体运营大区，并向用户确认。

    返回:
        list: 包含大区信息的字典列表，例如 [{'large_area_no': 'NO1', 'large_area_name': '杭州大区'}, ...]。
              如果查询失败或没有数据，则返回空列表。
    """
    sql = """
        SELECT
        large_area_no,
        large_area_name
        FROM
        large_area
        WHERE
        status = 1
        and `large_area_name` not LIKE '%测试%'
        and `large_area_name` not LIKE 'POP%';
    """
    result = execute_business_query(sql)
    if result.success:
        return result.data
    else:
        logger.error(f"Failed to get large areas: {result.error}")
        return []

def get_new_customers_of_today(bd_id_list: List[int] = None, city_list: List[str] = None) -> SQLQueryResult:
    """
    获取今日拉新门店列表。

    该函数查询今日有订单且历史无订单的新门店，这些门店被认为是今日拉新的客户。
    查询条件包括：
    1. 门店注册时间在最近60天内
    2. 最后下单时间为今日
    3. 今日GMV >= 25元
    4. 今日之前的GMV <= 0（即历史无订单）

    参数:
        bd_id_list: BD（业务代表）ID列表，用于过滤特定BD负责的门店。
        city_list: 城市列表，用于过滤特定城市的门店。

    返回:
        list: 包含新门店信息的字典列表，包含门店ID、名称、注册时间、地址、今日GMV、BD信息等。
              如果查询失败或没有数据，则返回空列表。
    """
    # 构建过滤条件
    filter = ""
    if bd_id_list and len(bd_id_list) > 0:
        # 优先使用bd_id过滤
        bd_ids_str = ','.join(map(str, bd_id_list))
        filter = f"AND fl.`admin_id` IN ({bd_ids_str})"
    elif city_list and len(city_list) > 0:
        # 使用city过滤
        city_names_str = ','.join([f"'{name}'" for name in city_list])
        filter = f"AND m.city IN ({city_names_str})"

    sql = f"""
        SELECT
          m.m_id as 门店ID,
          m.mname as 门店名称,
          m.register_time as 注册时间,
          m.province as 省份,
          m.city as 城市,
          m.area as 区县,
          od.today_gmv as 今日GMV,
          fl.admin_id as bd_id,
          fl.admin_name as bd_name
        FROM
          merchant m
          JOIN follow_up_relation fl ON m.m_id = fl.m_id
          JOIN (
            SELECT
              m_id,
              SUM( CASE WHEN order_time >= CURRENT_DATE THEN total_price ELSE 0 END) today_gmv,
              SUM( CASE WHEN order_time < CURRENT_DATE THEN total_price ELSE 0 END) before_today_gmv
            FROM
              orders
            WHERE
              order_time >= CURRENT_DATE - INTERVAL 60 DAY
              AND status IN (2, 3, 6)
            GROUP BY
              m_id
          ) od ON m.m_id = od.m_id
        WHERE
          m.register_time >= CURRENT_DATE - INTERVAL 60 DAY
          AND m.last_order_time >= CURRENT_DATE
          AND od.today_gmv >= 25
          AND od.before_today_gmv <= 0
          {filter}
        ORDER BY od.today_gmv DESC
    """

    result = execute_business_query(sql)
    if result.success:
        return result
    else:
        logger.error(f"获取今日拉新门店列表失败: {result.error}")
        return []


def get_high_valued_customers_starting_from_now(
    bd_id_list: List[int] = None,
    spu_threshold: int = 4,
    gmv_threshold: int = 2000,
    only_new_high_valued: bool = True
) -> SQLQueryResult:
    """
    获取截止当前时间刚刚达到高价值门槛的门店列表。

    高价值客户定义：
    1. SPU数量 >= spu_threshold（默认4个）
    2. 总配送GMV >= gmv_threshold（默认2000元）

    注意：不同运营大区的门槛值可能不同，AI需要根据具体大区调整参数。

    参数:
        bd_id_list: BD（业务代表）ID列表，用于过滤特定BD负责的门店。
        spu_threshold: SPU数量门槛，默认为4。不同大区可能有不同标准。
        gmv_threshold: GMV门槛，默认为2000元。不同大区可能有不同标准。
        only_new_high_valued: 是否只返回今日刚刚达到高价值门槛的客户。
                             True: 只返回今日之前还未达到门槛，但截止目前已达到门槛的客户
                             False: 返回截止目前所有达到门槛的高价值客户

    返回:
        list: 包含高价值门店信息的字典列表，包含门店ID、BD信息、SPU数量、GMV等信息。
              如果查询失败或没有数据，则返回空列表。
    """
    # 构建BD过滤条件
    bd_filter = ""
    if bd_id_list and len(bd_id_list) > 0:
        # 优先使用bd_id过滤
        bd_ids_str = ','.join(map(str, bd_id_list))
        bd_filter = f"AND fl.admin_id IN ({bd_ids_str})"

    # 构建新高价值客户的额外过滤条件
    new_customer_filter = ""
    if only_new_high_valued:
        new_customer_filter = f"""
        AND t.before_today_cnt < {spu_threshold}
        AND t.before_today_delivered_gmv < {gmv_threshold}
        """

    sql = f"""
        SELECT
          fl.admin_id as bd_id,
          fl.admin_name as bd_name,
          m.area_no as 客户运营服务区编号,
          t.m_id as 客户id,
          t.spu_cnt as 客户SPU数量,
          t.total_delivered_gmv as 客户总履约GMV,
          t.before_today_cnt as 客户截止昨日SPU数量,
          t.before_today_delivered_gmv as 客户截止昨日履约GMV,
          t.days_has_delivered_order as 客户履约日期列表
        FROM
        (SELECT
          od.m_id,
          COUNT(DISTINCT CASE WHEN c.type = 4 THEN c.id ELSE i.pd_id END) AS spu_cnt,
          SUM(odi.price * CASE WHEN od.type = 1 THEN dp.quantity ELSE odi.amount END) AS total_delivered_gmv,
          COUNT(DISTINCT CASE
              WHEN dp.delivery_time <= CURRENT_DATE
              THEN (CASE WHEN c.type = 4 THEN c.id ELSE i.pd_id END)
              ELSE NULL
          END) AS before_today_cnt,
          SUM(CASE
              WHEN dp.delivery_time <= CURRENT_DATE
              THEN (odi.price * CASE WHEN od.type = 1 THEN dp.quantity ELSE odi.amount END)
              ELSE 0
          END) AS before_today_delivered_gmv,
          GROUP_CONCAT(DISTINCT dp.delivery_time) as days_has_delivered_order
        FROM
          delivery_plan dp
          JOIN orders od ON od.order_no = dp.order_no
          JOIN order_item odi ON od.order_no = odi.order_no
          JOIN inventory i ON odi.sku = i.sku
          JOIN category c ON c.id = odi.category_id
        WHERE
          dp.delivery_time >= DATE_FORMAT(CURRENT_DATE, '%Y-%m-01')
          AND dp.delivery_time <= DATE_ADD(CURRENT_DATE, INTERVAL 1 DAY) -- 取明日履约的订单
          AND dp.delivery_time < DATE_ADD(LAST_DAY(CURDATE()), INTERVAL 1 DAY) -- 排除下个月履约的订单
          AND dp.status IN (3, 6)
          AND i.sub_type = 3
          AND od.type NOT IN (10, 30, 11)
        GROUP BY
          od.m_id) t
          JOIN follow_up_relation fl ON t.m_id = fl.m_id
          JOIN merchant m ON t.m_id = m.m_id
        WHERE
          t.spu_cnt >= {spu_threshold}
          AND t.total_delivered_gmv >= {gmv_threshold}
          {new_customer_filter}
          {bd_filter}
        ORDER BY t.total_delivered_gmv DESC
    """

    result = execute_business_query(sql)
    if result.success:
        return result
    else:
        logger.error(f"获取高价值客户列表失败: {result.error}")
        return SQLQueryResult(success=False, error=result.error)


tool_manager.register_as_function_tool(fetch_mysql_sql_result)
tool_manager.register_as_function_tool(get_sales_manager_team_members)
tool_manager.register_as_function_tool(get_table_sample_data)
tool_manager.register_as_function_tool(get_large_areas)
tool_manager.register_as_function_tool(get_new_customers_of_today)
tool_manager.register_as_function_tool(get_high_valued_customers_starting_from_now)


